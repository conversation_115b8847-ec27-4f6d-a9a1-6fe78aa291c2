import React from 'react';
import '../../styles/Card.css';

const Card = ({
  title = "Structural Design - 1",
  price = "₹XXXX",
  duration = "4 months",
  image,
  onInfoClick,
  onCardClick,
  className = "",
  showInfoIcon = true
}) => {

  const InfoIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="8" cy="8" r="7" stroke="currentColor" strokeWidth="1.5" fill="none"/>
      <path d="M8 12V8M8 4V4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    </svg>
  );

  return (
    <div 
      className={`card ${className}`}
      onClick={onCardClick}
      style={{ cursor: onCardClick ? 'pointer' : 'default' }}
    >
      <div className="card-content">
        {/* Image Section - Only render if image is provided */}
        {image && (
          <div style={{ 
            marginBottom: '16px',
            borderRadius: '8px',
            overflow: 'hidden',
            backgroundColor: '#FAFAFA'
          }}>
            <img 
              src={image} 
              alt={title}
              style={{
                width: '100%',
                height: '120px',
                objectFit: 'cover'
              }}
            />
          </div>
        )}

        {/* Title */}
        <h3 className="h4 text-text" style={{ margin: '0 0 12px 0' }}>
          {title}
        </h3>

        {/* Price and Duration Row */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: 'auto',
          paddingTop: '12px'
        }}>
          {/* Price */}
          <span className="body3-bold text-text">
            Price {price}
          </span>

          {/* Duration and Info Icon */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: 'var(--secondary-text-color)'
          }}>
            <span className="body3" style={{ color: 'var(--secondary-text-color)' }}>
              {duration}
            </span>
            {showInfoIcon && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onInfoClick?.();
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  padding: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  color: 'var(--secondary-text-color)',
                  borderRadius: '4px'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#F0F0F0';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = 'transparent';
                }}
              >
                <InfoIcon />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Card;  