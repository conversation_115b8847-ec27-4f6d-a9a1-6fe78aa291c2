import React from 'react';
import Card from '../ui/Card';
import '../../styles/DashboardMainContent.css';

const DashboardMainContent = ({
  activeTab,
  courses,
  onCourseClick
}) => {
  const renderHomeContent = () => (
    <div className="courses-grid">
      {courses.map((course) => (
        <Card
          key={course.id}
          title={course.title}
          price={course.price}
          duration={course.duration}
          image={course.image}
          alt={`${course.title} course thumbnail showing construction and engineering concepts`}
          onCardClick={() => onCourseClick(course.id)}
          className="course-card"
        />
      ))}
    </div>
  );



  const renderCategoriesContent = () => (
    <div className="categories-section">
      <h2 className="h2 text-primary">Categories</h2>
      <p className="body2 text-secondary">Categories content coming soon...</p>
    </div>
  );

  const renderCoursesContent = () => (
    <div className="my-courses-section">
      <h2 className="h2 text-primary">My Courses</h2>
      <p className="body2 text-secondary">Your enrolled courses will appear here...</p>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return renderHomeContent();
      case 'categories':
        return renderCategoriesContent();
      case 'courses':
        return renderCoursesContent();
      default:
        return renderHomeContent();
    }
  };

  return (
    <div className="dashboard-main-content">
      <div className="content-container">
        {renderContent()}
      </div>
    </div>
  );
};

export default DashboardMainContent;
