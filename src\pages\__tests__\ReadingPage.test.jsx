import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import ReadingPage from '../ReadingPage.jsx';

// Mock complex components that might have dependencies
vi.mock('../../components/layout/CourseStructure', () => ({
  default: ({ topics, activeTopicId, onSelectTopic }) => (
    <div data-testid="course-structure">
      <button onClick={() => onSelectTopic?.(topics?.[0]?.id)} data-testid="topic-button">
        {topics?.[0]?.title || 'Topic 1'}
      </button>
    </div>
  ),
}));

vi.mock('../../components/layout/MainContent', () => ({
  default: ({ topic, textSize }) => (
    <div data-testid="main-content" data-text-size={textSize}>
      <div data-testid="pdf-content">{topic?.title || 'PDF Content'}</div>
    </div>
  ),
}));

vi.mock('../../components/layout/ToolsPanel', () => ({
  default: ({ textSize, onTextSizeChange, currentTopic }) => (
    <div data-testid="tools-panel">
      <button onClick={() => onTextSizeChange?.('large')} data-testid="text-size-button">
        Change Text Size
      </button>
    </div>
  ),
}));

// Mock UI components
vi.mock('../../components/ui/LoadingSpinner', () => ({
  default: () => <div data-testid="loading-spinner">Loading...</div>,
}));

// Mock stores
vi.mock('../../stores', () => ({
  useAudioStore: (selector) => {
    const store = {
      initialize: vi.fn(() => () => {}), // Return cleanup function
      loadPagePlaylist: vi.fn(),
    };
    return selector(store);
  },
}));

// Mock hooks
vi.mock('../../hooks', () => ({
  useLocalStorage: vi.fn(() => ['normal', vi.fn()]),
}));

describe('ReadingPage', () => {
  const mockCourseData = {
    id: '1',
    title: 'Test Course',
    topics: [
      {
        id: 'topic1',
        title: 'Topic 1',
        pdfUrl: '/test.pdf',
        content: { heading: 'Test Heading' }
      }
    ]
  };

  const mockPdfData = mockCourseData.topics[0];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderWithRouter = (component) => {
    return render(
      <MemoryRouter>
        {component}
      </MemoryRouter>
    );
  };

  describe('Rendering', () => {
    it('should render reading page with all components', () => {
      renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      expect(screen.getByTestId('course-structure')).toBeInTheDocument();
      expect(screen.getByTestId('main-content')).toBeInTheDocument();
      expect(screen.getByTestId('tools-panel')).toBeInTheDocument();
    });

    it('should render with default data when no courseData provided', () => {
      renderWithRouter(<ReadingPage courseData={{ topics: [] }} pdfData={mockPdfData} />);

      expect(screen.getByTestId('course-structure')).toBeInTheDocument();
      expect(screen.getByTestId('main-content')).toBeInTheDocument();
      expect(screen.getByTestId('tools-panel')).toBeInTheDocument();
    });

    it('should pass correct props to MainContent', () => {
      renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      const mainContent = screen.getByTestId('main-content');
      expect(mainContent).toHaveAttribute('data-text-size', 'normal');
      expect(screen.getByTestId('pdf-content')).toHaveTextContent('Topic 1');
    });
  });

  describe('Component structure', () => {
    it('should have correct layout structure', () => {
      const { container } = renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      expect(container.querySelector('.app-container')).toBeInTheDocument();
      expect(container.querySelector('.course-structure-sidebar')).toBeInTheDocument();
      expect(container.querySelector('.main-content-panel')).toBeInTheDocument();
      expect(container.querySelector('.tools-panel-sidebar')).toBeInTheDocument();
    });

    it('should render audio element', () => {
      const { container } = renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      const audioElement = container.querySelector('audio');
      expect(audioElement).toBeInTheDocument();
      expect(audioElement).toHaveStyle({ display: 'none' });
    });
  });

  describe('Component interactions', () => {
    it('should handle topic selection', () => {
      renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      const topicButton = screen.getByTestId('topic-button');
      expect(topicButton).toBeInTheDocument();

      // Click should not throw error
      fireEvent.click(topicButton);
    });

    it('should handle text size changes', () => {
      renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      const textSizeButton = screen.getByTestId('text-size-button');
      expect(textSizeButton).toBeInTheDocument();

      // Click should not throw error
      fireEvent.click(textSizeButton);
    });
  });
});
