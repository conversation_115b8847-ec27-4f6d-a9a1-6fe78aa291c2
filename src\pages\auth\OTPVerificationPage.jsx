import { useEffect, useState } from 'react';
import OtpInput from 'react-otp-input';
import { useLocation, useNavigate } from 'react-router-dom';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import { initializeServices } from '../../services/index.js';
import '../../styles/LoginPage.css';
import '../../styles/OTPVerification.css';

const OTPVerificationPage = () => {
  const [otp, setOtp] = useState('');
  const [error, setError] = useState('');
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);

  const navigate = useNavigate();
  const location = useLocation();
  const email = location.state?.email || '';
  const verificationToken = location.state?.verificationToken || '';

  // Initialize services
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);
      } catch (error) {
        console.error('Failed to initialize services:', error);
        setError('Failed to initialize application. Please refresh the page.');
      }
    };

    initServices();
  }, []);

  useEffect(() => {
    if (!email) {
      navigate('/signup');
      return;
    }

    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [email, navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP.');
      return;
    }

    if (!authService) {
      setError('Service not initialized. Please refresh the page.');
      return;
    }

    setIsLoading(true);

    try {
      // Debug logging
      console.log('🔍 OTP Validation Debug Info:', {
        email,
        otp,
        verificationToken,
        purpose: 'sign_up',
        tokenLength: verificationToken?.length || 0
      });

      // Call the validateOTP API with the new method
      const result = await authService.validateOTP(
        email,
        otp,
        verificationToken,
        'sign_up'
      );

      console.log('📋 OTP Validation Result:', result);

      if (result.success) {
        // Debug the OTP validation response structure
        console.log('📋 OTP Validation Full Response:', result);
        console.log('📋 OTP Validation - Data Object:', result.data);
        console.log('📋 OTP Validation - Nested Data:', result.data?.data);

        // Get the token from OTP validation response - try both possible locations
        const newToken = result.data?.data?.token || result.data?.token || verificationToken;

        console.log('🎯 Navigating to password creation with token:', {
          email,
          originalToken: verificationToken ? `${verificationToken.substring(0, 10)}...` : 'No original token',
          newToken: newToken ? `${newToken.substring(0, 10)}...` : 'No new token',
          tokenFromDataData: result.data?.data?.token ? 'Found in data.data.token' : 'Not in data.data.token',
          tokenFromData: result.data?.token ? 'Found in data.token' : 'Not in data.token',
          usingToken: newToken !== verificationToken ? 'New token from OTP response' : 'Original token from email verification'
        });

        // Navigate to create password screen on success
        navigate('/set-password', {
          state: {
            email,
            // Pass the token from OTP validation response (this is what the password API needs)
            verificationToken: newToken
          }
        });
      } else {
        // Display exact error message from backend
        setError(result.error || 'OTP validation failed');
      }
    } catch (error) {
      console.error('OTP validation error:', error);
      // Display exact error message from backend
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'OTP validation failed';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (canResend && authService) {
      setIsLoading(true);
      setError('');

      try {
        // Call the resend email verification API
        const result = await authService.resendEmailVerification(email);

        if (result.success) {
          // Reset timer and UI state
          setTimer(60);
          setCanResend(false);
          setOtp('');
          setError('');

          // Start countdown timer
          const interval = setInterval(() => {
            setTimer((prev) => {
              if (prev <= 1) {
                setCanResend(true);
                clearInterval(interval);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);

          // Show success message briefly
          setError(''); // Clear any previous errors
          // You could add a success state here if needed
        } else {
          setError(result.error || 'Failed to resend OTP. Please try again.');
        }
      } catch (error) {
        console.error('Resend OTP error:', error);
        setError('An unexpected error occurred. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="login-container">
      <Header />
      <AnimationBox className="otp-verification-box">
        <h4 className='h4 otp-title'>Verify Account</h4>

        <div className="otp-description">
          <p className="body2 otp-instruction">Enter OTP sent to email:</p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <div className="otp-container">
              <OtpInput
                value={otp}
                onChange={setOtp}
                numInputs={6}
                renderSeparator={<span className="otp-separator"></span>}
                renderInput={(inputProps, idx) => (
                  <input {...inputProps} className="otp-input" key={idx} />
                )}
                inputType="number"
                shouldAutoFocus={true}
              />
            </div>
            {error && <div className="error-message">{error}</div>}
          </div>

          <div className="otp-timer-section">
            <span className="otp-timer">{formatTime(timer)} - 3:00</span>
            {canResend ? (
              <button
                type="button"
                className="resend-link"
                onClick={handleResendOTP}
                disabled={isLoading}
              >
                Resend OTP?
              </button>
            ) : (
              <span className="resend-disabled">Resend OTP?</span>
            )}
          </div>

          <button
            type="submit"
            className="otp-next-button"
            disabled={otp.length !== 6 || isLoading}
          >
            {isLoading ? 'Verifying...' : 'Next'}
          </button>
        </form>

        <button
          className="otp-go-back"
          onClick={() => navigate('/signup')}
        >
          Go Back
        </button>
      </AnimationBox>
    </div>
  );
};

export default OTPVerificationPage;