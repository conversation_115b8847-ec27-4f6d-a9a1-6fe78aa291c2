{"name": "poc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "dev:prod": "vite --mode production", "start": "vite --mode development", "start:dev": "vite --mode development", "start:prod": "vite --mode production", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview", "preview:dev": "npm run build:dev && vite preview", "preview:prod": "npm run build:prod && vite preview", "lint": "eslint .", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "test:coverage:ui": "vitest --ui --coverage", "deploy:dev": "npm run build:dev && firebase use development && firebase deploy --only hosting", "deploy:prod": "npm run build:prod && firebase use production && firebase deploy --only hosting", "firebase:dev": "firebase use development", "firebase:prod": "firebase use production", "firebase:status": "firebase use", "validate": "node scripts/validate-environment.js", "validate:env": "node scripts/validate-environment.js", "test:config": "node scripts/test-config.js"}, "dependencies": {"axios": "^1.10.0", "firebase": "^12.0.0", "lucide-react": "^0.525.0", "pdfjs-dist": "^5.3.93", "react": "^19.1.0", "react-dom": "^19.1.0", "react-google-recaptcha": "^3.1.0", "react-otp-input": "^3.1.1", "react-pdf": "^10.0.1", "react-pdf-highlighter": "^8.0.0-rc.0", "react-router-dom": "^7.6.3", "slate": "^0.117.2", "slate-history": "^0.113.1", "slate-react": "^0.117.3", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.25.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "vite": "^6.3.5", "vitest": "^3.2.4"}}