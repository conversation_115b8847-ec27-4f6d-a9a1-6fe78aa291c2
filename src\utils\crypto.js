/**
 * Hash password using SHA-256 (deterministic - same password always produces same hash)
 * @param {string} password - Plain text password
 * @returns {Promise<string>} SHA-256 hash of the password in hex format
 */
export async function hashPasswordSHA256(password) {
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);

    // Convert Array<PERSON>uffer to hex string
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    return hashHex;
  } catch (error) {
    console.error('SHA-256 password hashing failed:', error);
    throw new Error('Failed to hash password with SHA-256');
  }
}

/**
 * Encrypt password using AES-GCM with deterministic IV for API transmission
 * This ensures the same password always produces the same encrypted string
 * @param {string} password - Plain text password
 * @param {string} keyStr - Encryption key (default: "somayyaacademy")
 * @returns {Promise<string>} Encrypted password in format "iv:ciphertext"
 */
export async function encryptPassword(password, keyStr = "somayyaacademy") {
  try {
    const enc = new TextEncoder();

    // 1. Hash the key using SHA-256
    const keyBuffer = await crypto.subtle.digest("SHA-256", enc.encode(keyStr));
    const aesKey = keyBuffer.slice(0, 16); // First 16 bytes (128-bit key)

    // 2. Import the key
    const cryptoKey = await crypto.subtle.importKey("raw", aesKey, "AES-GCM", false, ["encrypt"]);

    // 3. Generate deterministic IV from password hash (first 12 bytes)
    // This ensures same password always produces same IV
    const passwordHash = await crypto.subtle.digest("SHA-256", enc.encode(password + keyStr));
    const iv = new Uint8Array(passwordHash.slice(0, 12));

    // 4. Encrypt
    const encrypted = await crypto.subtle.encrypt(
        { name: "AES-GCM", iv },
        cryptoKey,
        enc.encode(password)
    );

    // 5. Encode to base64
    const ivBase64 = btoa(String.fromCharCode(...iv));
    const cipherBase64 = btoa(String.fromCharCode(...new Uint8Array(encrypted)));

    return `${ivBase64}:${cipherBase64}`;
  } catch (error) {
    console.error('Password encryption failed:', error);
    throw new Error('Failed to encrypt password');
  }
}

/**
 * Decrypt password using AES-GCM
 * @param {string} encryptedPassword - Encrypted password in format "iv:ciphertext"
 * @param {string} keyStr - Decryption key (default: "somayyaacademy")
 * @returns {Promise<string>} Decrypted password
 */
export async function decryptPassword(encryptedPassword, keyStr = "somayyaacademy") {
  try {
    const parts = encryptedPassword.split(':');
    if (parts.length !== 2) {
      throw new Error('Invalid encrypted password format');
    }

    const [ivBase64, cipherBase64] = parts;
    const enc = new TextEncoder();
    const dec = new TextDecoder();

    // 1. Hash the key using SHA-256
    const keyBuffer = await crypto.subtle.digest("SHA-256", enc.encode(keyStr));
    const aesKey = keyBuffer.slice(0, 16); // First 16 bytes (128-bit key)

    // 2. Import the key
    const cryptoKey = await crypto.subtle.importKey("raw", aesKey, "AES-GCM", false, ["decrypt"]);

    // 3. Decode IV and ciphertext from base64
    const iv = new Uint8Array(atob(ivBase64).split('').map(c => c.charCodeAt(0)));
    const ciphertext = new Uint8Array(atob(cipherBase64).split('').map(c => c.charCodeAt(0)));

    // 4. Decrypt
    const decrypted = await crypto.subtle.decrypt(
      { name: "AES-GCM", iv },
      cryptoKey,
      ciphertext
    );

    return dec.decode(decrypted);
  } catch (error) {
    console.error('Password decryption failed:', error);
    throw new Error('Failed to decrypt password');
  }
}

/**
 * Generate a random salt
 * @param {number} length - Salt length in bytes (default: 16)
 * @returns {string} Base64 encoded salt
 */
export function generateSalt(length = 16) {
  const salt = new Uint8Array(length);
  crypto.getRandomValues(salt);
  return btoa(String.fromCharCode(...salt));
}

/**
 * Hash password with salt using SHA-256 (simpler approach for better test compatibility)
 * @param {string} password - Plain text password
 * @param {string} salt - Salt (if not provided, generates new one)
 * @returns {Promise<{hash: string, salt: string}>} Hash and salt
 */
export async function hashPassword(password, salt = null) {
  try {
    if (!salt) {
      salt = generateSalt();
    }

    const enc = new TextEncoder();
    // Combine password and salt
    const combined = password + salt;
    const data = enc.encode(combined);

    // Hash using SHA-256
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    return { hash, salt };
  } catch (error) {
    console.error('Password hashing failed:', error);
    throw new Error('Failed to hash password');
  }
}

/**
 * Verify password against hash
 * @param {string} password - Plain text password
 * @param {string} hash - Stored hash
 * @param {string} salt - Salt used for hashing
 * @returns {Promise<boolean>} True if password matches
 */
export async function verifyPassword(password, hash, salt) {
  try {
    const { hash: computedHash } = await hashPassword(password, salt);
    return computedHash === hash;
  } catch (error) {
    console.error('Password verification failed:', error);
    return false;
  }
}

/**
 * Generate a secure random token
 * @param {number} length - Token length in bytes (default: 32)
 * @returns {string} Base64 encoded token
 */
export function generateSecureToken(length = 32) {
  const token = new Uint8Array(length);
  crypto.getRandomValues(token);
  return btoa(String.fromCharCode(...token));
}

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {object} Validation result with criteria
 */
export function validatePasswordStrength(password) {
  const result = {
    minLength: password.length >= 8,
    hasUpperCase: /[A-Z]/.test(password),
    hasLowerCase: /[a-z]/.test(password),
    hasNumbers: /\d/.test(password),
    hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password), // Singular to match tests
    isValid: false,
    score: 0
  };

  // Calculate score
  let score = 0;
  if (result.minLength) score++;
  if (result.hasUpperCase) score++;
  if (result.hasLowerCase) score++;
  if (result.hasNumbers) score++;
  if (result.hasSpecialChar) score++;

  result.score = score;
  result.isValid = score >= 3; // At least 3 criteria must be met

  // Add strength classification
  if (score >= 5) {
    result.strength = 'strong';
  } else if (score >= 3) {
    result.strength = 'medium';
  } else {
    result.strength = 'weak';
  }

  return result;
}