import React from 'react';
import { IoInformationCircleOutline } from 'react-icons/io5';

import '../../styles/Card.css';

const Card = ({
  title = "Structural Design - 1",
  price = "₹XXXX",
  duration = "4 months",
  image,
  alt,
  onInfoClick,
  onCardClick,
  className = "",
  showInfoIcon = true
}) => {

  return (
    <div
      className={`card ${onCardClick ? 'card-clickable' : ''} ${className}`}
      onClick={onCardClick}
    >
      <div className="card-content">
        {/* Image Section - Only render if image is provided */}
        {image && (
          <div className="card-image-container">
            <img
              src={image}
              alt={alt || title}
              className="card-image"
            />
          </div>
        )}

        {/* Title */}
        <h3 className="h4 card-title">
          {title}
        </h3>

        {/* Price and Duration Row */}
        <div className="card-footer">
          {/* Price */}
          <span className="body3-bold card-price">
            Price {price}
          </span>

          {/* Duration and Info Icon */}
          <div className="card-duration-section">
            <span className="body3 card-duration">
              {duration}
            </span>
            {showInfoIcon && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onInfoClick?.();
                }}
                className="card-info-button"
              >
                <IoInformationCircleOutline size={16} />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Card;