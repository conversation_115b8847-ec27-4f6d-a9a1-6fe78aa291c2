import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import { ReCaptcha } from '../../components/ui';
import '../../styles/LoginPage.css';
import { auth, googleProvider, microsoftProvider, facebookProvider } from '../../config/firebase';
import { signInWithPopup } from 'firebase/auth';


const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const [showPassword, setShowPassword] = useState(false);
  const [captchaToken, setCaptchaToken] = useState(null);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [captchaError, setCaptchaError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const recaptchaRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const login = useAuthStore((state) => state.login);

  // Check for success message from UserDetails page
  useEffect(() => {
    if (location.state?.message) {
      setSuccessMessage(location.state.message);
      if (location.state?.email) {
        setEmail(location.state.email);
      }
      // Clear the message from location state to prevent it from persisting
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    let valid = true;
    setEmailError('');
    setPasswordError('');
    setCaptchaError('');

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('Email is required.');
      valid = false;
    } else if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address.');
      valid = false;
    }

    if (!password) {
      setPasswordError('Password is required.');
      valid = false;
    }

    if (!captchaToken) {
      setCaptchaError('Please complete the CAPTCHA.');
      valid = false;
    }

    if (!valid) return;

    try {
      const success = await login(email, password, captchaToken);
      if (success) {
        navigate('/reading-screen');
      } else {
        // Get the exact error message from the login attempt
        const loginResult = await useAuthStore.getState().login(email, password, captchaToken);
        if (!loginResult.success && loginResult.error) {
          setPasswordError(loginResult.error);
        } else {
          setPasswordError('Login failed');
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      // Display exact error message from backend
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'Login failed';
      setPasswordError(errorMessage);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      const result = await signInWithPopup(auth, googleProvider);
      console.log("🔍 Google sign-in result:", result.user);

      const loginResult = await useAuthStore.getState().thirdPartyLogin(result.user);
      console.log('🔍 Google login result received in LoginPage:', loginResult);

      if (loginResult && loginResult.success) {
        // Check if profile is set to determine redirect path
        console.log('🔍 Checking profile status:', loginResult.is_profile_set, typeof loginResult.is_profile_set);
        if (loginResult.is_profile_set) {
          console.log('✅ Profile is set, navigating to /reading-screen');
          navigate('/reading-screen');
        } else {
          console.log('✅ Profile NOT set, navigating to /user-details');
          navigate('/user-details'); // Redirect to user details page if profile not set
        }
      } else {
        console.error('Google sign-in failed - backend authentication unsuccessful');
        // You might want to show an error message to the user here
      }
    } catch (error) {
      console.error("Error signing in with Google:", error);
      // You might want to show an error message to the user here
    }
  };

  const handleMicrosoftSignIn = async () => {
    try {
      const result = await signInWithPopup(auth, microsoftProvider);
      console.log("🔍 Microsoft sign-in result:", result.user);

      const loginResult = await useAuthStore.getState().thirdPartyLogin(result.user);
      console.log('🔍 Microsoft login result received in LoginPage:', loginResult);

      if (loginResult && loginResult.success) {
        // Check if profile is set to determine redirect path
        console.log('🔍 Checking profile status:', loginResult.is_profile_set, typeof loginResult.is_profile_set);
        if (loginResult.is_profile_set) {
          console.log('✅ Profile is set, navigating to /reading-screen');
          navigate('/reading-screen');
        } else {
          console.log('✅ Profile NOT set, navigating to /user-details');
          navigate('/user-details'); // Redirect to user details page if profile not set
        }
      } else {
        console.error('Microsoft sign-in failed - backend authentication unsuccessful');
        // You might want to show an error message to the user here
      }
    } catch (error) {
      console.error("Error signing in with Microsoft:", error);
      // You might want to show an error message to the user here
    }
  };

  const handleFacebookSignIn = async () => {
    try {
      const result = await signInWithPopup(auth, facebookProvider);
      console.log("🔍 Facebook sign-in result:", result.user);

      const loginResult = await useAuthStore.getState().thirdPartyLogin(result.user);
      console.log('🔍 Facebook login result received in LoginPage:', loginResult);

      if (loginResult && loginResult.success) {
        // Check if profile is set to determine redirect path
        console.log('🔍 Checking profile status:', loginResult.is_profile_set, typeof loginResult.is_profile_set);
        if (loginResult.is_profile_set) {
          console.log('✅ Profile is set, navigating to /reading-screen');
          navigate('/reading-screen');
        } else {
          console.log('✅ Profile NOT set, navigating to /user-details');
          navigate('/user-details'); // Redirect to user details page if profile not set
        }
      } else {
        console.error('Facebook sign-in failed - backend authentication unsuccessful');
        // You might want to show an error message to the user here
      }
    } catch (error) {
      console.error("Error signing in with Facebook:", error);
      // You might want to show an error message to the user here
    }
  };


  return (
    <div className="login-container">

      <Header />
      <AnimationBox className="login-box">

        <h4 className='h4'>Sign In</h4>

        {/* Success message from UserDetails completion */}
        {successMessage && (
          <div className="success-message" style={{
            backgroundColor: '#d4edda',
            color: '#155724',
            padding: '10px',
            borderRadius: '4px',
            marginBottom: '15px',
            border: '1px solid #c3e6cb'
          }}>
            {successMessage}
          </div>
        )}

        <div className='social-login'>
          <button className='social-btn google body2 ' onClick={handleGoogleSignIn}>Continue with Google<img src='/icons/google.svg' alt='google' /></button>
          <button className='social-btn apple body2'>Continue with Apple<img src='/icons/apple.svg' alt='google' /></button>
          <button className='social-btn microsoft body2' onClick={handleMicrosoftSignIn}>Continue with Microsoft<img src='/icons/microsoft.svg' alt='google' /></button>
          <button className='social-btn facebook body2' onClick={handleFacebookSignIn}>Continue with Facebook<img src='/icons/facebook.svg' alt='google' /></button>
        </div>

        <div className="divider"><span>OR</span></div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="text"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."

            />

            {emailError && <div className="error-message">{emailError}</div>}
          </div>
          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <div className="password-wrapper">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password..."

              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setShowPassword((prev) => !prev)}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <EyeIcon size={20} /> : <EyeOffIcon size={20} />}
              </button>
            </div>
            {passwordError && <div className="error-message">{passwordError}</div>}
          </div>

          <ReCaptcha
            ref={recaptchaRef}
            onChange={(token) => setCaptchaToken(token)}
            showError={false}
          />
          {captchaError && <div className="error-message">{captchaError}</div>}

          <button type="submit" className="body3-bold login-button">Sign in</button>
        </form>
        <div className='body4 forgot-password'><a href="/forgot-password">Forgot password?</a></div>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>New to the platform?</span>
          <button
            className='body3-bold'
            onClick={() => navigate('/signup')}
          >
            Sign up
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default LoginPage;