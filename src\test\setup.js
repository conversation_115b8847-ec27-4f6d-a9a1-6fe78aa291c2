import '@testing-library/jest-dom';
import { vi, afterEach } from 'vitest';

// Mock Firebase (legacy)
vi.mock('../firebase.js', () => ({
  auth: {
    currentUser: null,
    signOut: vi.fn(),
  },
  googleProvider: {},
  microsoftProvider: {},
  facebookProvider: {},
}));

// Mock Firebase (new unified config)
vi.mock('../config/firebase.js', () => ({
  auth: {
    currentUser: null,
    signOut: vi.fn(),
  },
  googleProvider: {},
  microsoftProvider: {},
  facebookProvider: {},
  default: {},
}));

// Mock Firebase auth functions
vi.mock('firebase/auth', () => ({
  signInWithPopup: vi.fn(),
  signOut: vi.fn(),
  onAuthStateChanged: vi.fn(),
  GoogleAuthProvider: vi.fn(),
  OAuthProvider: vi.fn(),
}));

// Mock environment configuration (new synchronous approach)
vi.mock('../config/environment.js', () => ({
  default: {
    api: { baseUrl: 'http://localhost:3000/api' },
    firebase: {
      apiKey: "test-api-key",
      authDomain: "test.firebaseapp.com",
      projectId: "test-project",
      storageBucket: "test.firebasestorage.app",
      messagingSenderId: "123456789",
      appId: "1:123456789:web:test"
    },
    env: 'test',
    isDevelopment: true,
    isProduction: false,
    runtime: {
      viteMode: 'test',
      viteDev: true,
      viteProd: false,
      detectedEnvironment: 'development'
    }
  }
}));

// Mock legacy environment imports (for backward compatibility)
vi.mock('../../environment/development.js', () => ({
  default: {
    api: { baseUrl: 'http://localhost:3000/api' },
    firebase: {
      apiKey: "test-api-key-dev",
      authDomain: "test-dev.firebaseapp.com",
      projectId: "test-dev",
      storageBucket: "test-dev.firebasestorage.app",
      messagingSenderId: "123456789",
      appId: "1:123456789:web:test-dev"
    },
    env: 'development',
    isDevelopment: true,
    isProduction: false,
  }
}));

vi.mock('../../environment/production.js', () => ({
  default: {
    api: { baseUrl: 'https://api.somayya-academy.com' },
    firebase: {
      apiKey: "test-api-key-prod",
      authDomain: "test-prod.firebaseapp.com",
      projectId: "test-prod",
      storageBucket: "test-prod.firebasestorage.app",
      messagingSenderId: "987654321",
      appId: "1:987654321:web:test-prod"
    },
    env: 'production',
    isDevelopment: false,
    isProduction: true,
  }
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock API client and services
vi.mock('../api/index.js', () => ({
  apiClient: {
    initialize: vi.fn().mockResolvedValue({}),
    post: vi.fn().mockResolvedValue({
      success: true,
      data: {
        statusCode: 200,
        data: {
          userId: 'test-user-id',
          userEmailId: '<EMAIL>',
          token: 'test-jwt-token',
          lastLogoutTime: null
        }
      }
    }),
    get: vi.fn().mockResolvedValue({ data: { success: true } }),
    put: vi.fn().mockResolvedValue({ data: { success: true } }),
    delete: vi.fn().mockResolvedValue({ data: { success: true } }),
    setAuthToken: vi.fn().mockResolvedValue(undefined),
  },
  API_ENDPOINTS: {
    auth: {
      emailVerification: '/auth/email-verification',
      validateOTP: '/auth/validate-otp',
      setPassword: '/auth/set-password',
      signin: '/auth/signin',
      login: '/auth/login',
      socialLogin: '/auth/social-login',
      logout: '/auth/logout',
      signup: '/auth/signup',
      createAccount: '/auth/create-account',
    }
  },
  withErrorHandling: vi.fn((fn) => fn()),
}));

// Mock AuthService
vi.mock('../services/authService.js', () => ({
  default: class MockAuthService {
    constructor() {
      this.apiClient = {
        post: vi.fn().mockResolvedValue({
          success: true,
          data: {
            statusCode: 200,
            data: {
              userId: 'test-user-id',
              userEmailId: '<EMAIL>',
              token: 'test-jwt-token',
              lastLogoutTime: null
            }
          }
        }),
        setAuthToken: vi.fn().mockResolvedValue(undefined),
      };
    }

    async signin(email, password, recaptchaToken) {
      // Simulate API failure for invalid credentials
      if (email === '<EMAIL>' ||
          email === '<EMAIL>' ||
          password === 'wrongpass') {
        throw new Error('Invalid credentials');
      }

      return {
        success: true,
        data: {
          statusCode: 200,
          data: {
            userId: 'test-user-id',
            userEmailId: email,
            token: 'test-jwt-token',
            lastLogoutTime: null
          }
        }
      };
    }

    async socialLogin(socialData) {
      return {
        success: true,
        data: {
          statusCode: 200,
          data: {
            userId: socialData.socialUserId,
            userEmailId: socialData.email,
            token: 'test-jwt-token',
            is_profile_set: true
          }
        }
      };
    }

    async logout() {
      return { success: true };
    }
  }
}));

// Mock crypto utilities - but allow tests to override
vi.mock('../utils/crypto.js', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    // Only provide fallback mocks for authStore tests
    encryptPassword: vi.fn().mockResolvedValue('encrypted-password'),
    hashPassword: vi.fn().mockResolvedValue({ hash: 'hashed-password', salt: 'mock-salt' }),
    hashPasswordSHA256: vi.fn().mockResolvedValue(new ArrayBuffer(32)),
    generateSalt: vi.fn().mockReturnValue('mock-salt'),
    verifyPassword: vi.fn().mockResolvedValue(true),
    generateSecureToken: vi.fn().mockReturnValue('mock-token'),
    validatePasswordStrength: vi.fn().mockReturnValue({ isValid: true, score: 5 }),
    decryptPassword: vi.fn().mockResolvedValue('decrypted-password'),
  };
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  warn: vi.fn(),
  error: vi.fn(),
  log: vi.fn(),
};

// Clean up after each test
afterEach(() => {
  vi.clearAllMocks();
  localStorage.clear();
  sessionStorage.clear();
});
