import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import SideNavBar from '../components/layout/SideNavBar';
import DashboardMainContent from '../components/layout/DashboardMainContent';
import '../styles/Dashboard.css';

const Dashboard = () => {
  const navigate = useNavigate();
  const { currentUser, logout, isAuthenticated } = useAuthStore();
  const [activeTab, setActiveTab] = useState('home');

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Sample course data matching the image
  const courses = [
    {
      id: 1,
      title: "Structural Design - 1",
      price: "₹XXXX",
      duration: "4 months",
      image: null, // Will use CSS placeholder
    },
    {
      id: 2,
      title: "Structural Design - 2",
      price: "₹XXXX",
      duration: "4 months",
      image: null,
    },
    {
      id: 3,
      title: "Structural Design - 3",
      price: "₹XXXX",
      duration: "4 months",
      image: null,
    },
    {
      id: 4,
      title: "Strength of Materials",
      price: "₹XXXX",
      duration: "4 months",
      image: null,
    },
    {
      id: 5,
      title: "Structural Analysis",
      price: "₹XXXX",
      duration: "4 months",
      image: null,
    },
    {
      id: 6,
      title: "Engineering Mechanics",
      price: "₹XXXX",
      duration: "4 months",
      image: null,
    }
  ];

  // Handle tab change
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleCourseClick = (courseId) => {
    // Navigate to course details or reading screen
    navigate(`/reading-screen?course=${courseId}`);
  };

  const handleCourseInfo = (courseId) => {
    // Show course info modal or navigate to course info page
    console.log('Show info for course:', courseId);
  };

  return (
    <div className="dashboard-container">
      <SideNavBar
        activeTab={activeTab}
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <DashboardMainContent
        activeTab={activeTab}
        courses={courses}
        currentUser={currentUser}
        onCourseClick={handleCourseClick}
        onCourseInfo={handleCourseInfo}
      />
    </div>
  );
};

export default Dashboard;
