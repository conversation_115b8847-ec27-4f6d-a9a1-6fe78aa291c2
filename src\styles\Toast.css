/* Toast Container */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
  pointer-events: none;
}

/* Individual Toast */
.toast {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: flex-start;
  padding: 16px;
  min-width: 300px;
  max-width: 400px;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease-in-out;
  pointer-events: auto;
  border-left: 4px solid #ccc;
}

.toast--visible {
  opacity: 1;
  transform: translateX(0);
}

.toast--exiting {
  opacity: 0;
  transform: translateX(100%);
}

/* Toast Types */
.toast--success {
  border-left-color: #4caf50;
}

.toast--error {
  border-left-color: #f44336;
}

.toast--warning {
  border-left-color: #ff9800;
}

.toast--info {
  border-left-color: #2196f3;
}

/* Toast Icon */
.toast__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
  font-size: 14px;
  font-weight: bold;
}

.toast--success .toast__icon {
  background-color: #4caf50;
  color: white;
}

.toast--error .toast__icon {
  background-color: #f44336;
  color: white;
}

.toast--warning .toast__icon {
  background-color: #ff9800;
  color: white;
}

.toast--info .toast__icon {
  background-color: #2196f3;
  color: white;
}

/* Toast Content */
.toast__content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.toast__message {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  word-wrap: break-word;
}

/* Toast Close Button */
.toast__close {
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  padding: 0;
  margin-left: 12px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.toast__close:hover {
  background-color: #f5f5f5;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .toast {
    min-width: auto;
    max-width: none;
  }
}

/* Animation for multiple toasts */
.toast:not(:last-child) {
  margin-bottom: 8px;
}