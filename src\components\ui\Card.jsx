import React from 'react';
import '../../styles/Card.css';

const Card = ({
  title = "Structural Design - 1",
  price = "₹XXXX",
  duration = "4 months",
  image,
  onInfoClick,
  onCardClick,
  className = "",
  showInfoIcon = true
}) => {

  const InfoIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="8" cy="8" r="7" stroke="currentColor" strokeWidth="1.5" fill="none"/>
      <path d="M8 12V8M8 4V4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
    </svg>
  );

  return (
    <div
      className={`card ${onCardClick ? 'card-clickable' : ''} ${className}`}
      onClick={onCardClick}
    >
      <div className="card-content">
        {/* Image Section - Only render if image is provided */}
        {image && (
          <div className="card-image-container">
            <img
              src={image}
              alt={title}
              className="card-image"
            />
          </div>
        )}

        {/* Title */}
        <h3 className="h4 text-text card-title">
          {title}
        </h3>

        {/* Price and Duration Row */}
        <div className="card-footer">
          {/* Price */}
          <span className="body3-bold text-text card-price">
            Price {price}
          </span>

          {/* Duration and Info Icon */}
          <div className="card-duration-section">
            <span className="body3 card-duration">
              {duration}
            </span>
            {showInfoIcon && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onInfoClick?.();
                }}
                className="card-info-button"
              >
                <InfoIcon />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Card;