import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import SideNavBar from '../components/layout/SideNavBar';
import '../styles/Profile.css';

const Profile = () => {
  const navigate = useNavigate();
  const { currentUser, isAuthenticated, logout } = useAuthStore();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Handle tab change - navigate to different routes
  const handleTabChange = (tabId) => {
    if (tabId === 'home') {
      navigate('/dashboard');
    } else if (tabId === 'profile') {
      // Already on profile page, do nothing
      return;
    }
    // Add more navigation logic for future tabs
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <div className="profile-container">
      <SideNavBar
        activeTab="profile"
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <div className="profile-main-content">
        <div className="profile-content-container">
          <div className="profile-section">
            <h1 className="h2 text-primary">Profile</h1>
            <div className="profile-info">
              <div className="profile-field">
                <span className="body2-bold profile-label">Email:</span>
                <span className="body2 text-primary profile-value">{currentUser?.email}</span>
              </div>
              <div className="profile-field">
                <span className="body2-bold profile-label">User ID:</span>
                <span className="body2 text-primary profile-value">{currentUser?.id}</span>
              </div>
              {currentUser?.name && (
                <div className="profile-field">
                  <span className="body2-bold profile-label">Name:</span>
                  <span className="body2 text-primary profile-value">{currentUser.name}</span>
                </div>
              )}
              {currentUser?.phone && (
                <div className="profile-field">
                  <span className="body2-bold profile-label">Phone:</span>
                  <span className="body2 text-primary profile-value">{currentUser.phone}</span>
                </div>
              )}
              {/* Add more profile fields as needed */}
            </div>
            
            {/* Profile Actions */}
            <div className="profile-actions">
              <button className="body3-bold profile-edit-btn">
                Edit Profile
              </button>
              <button className="body3-bold profile-settings-btn">
                Settings
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
