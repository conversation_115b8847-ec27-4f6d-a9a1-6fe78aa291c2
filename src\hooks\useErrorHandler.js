import { useState, useCallback } from 'react';
import { getErrorMessage } from '../utils/helpers.js';

/**
 * Custom hook for error handling with toast notifications - preserves exact error messages
 * @returns {Object} Error handling utilities
 */
export const useErrorHandler = () => {
  const [errors, setErrors] = useState([]);

  const addError = useCallback((error, context = '') => {
    const errorMessage = getErrorMessage(error);
    const errorId = Date.now().toString();

    const errorObject = {
      id: errorId,
      message: errorMessage,
      context,
      timestamp: new Date().toISOString(),
    };

    setErrors(prev => [...prev, errorObject]);

    // Auto-remove error after 5 seconds
    setTimeout(() => {
      setErrors(prev => prev.filter(err => err.id !== errorId));
    }, 5000);

    // Log error for debugging
    console.error(`Error in ${context}:`, error);

    return errorId;
  }, []);

  const removeError = useCallback((errorId) => {
    setErrors(prev => prev.filter(err => err.id !== errorId));
  }, []);

  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  const handleAsyncError = useCallback(async (asyncFn, context = '') => {
    try {
      return await asyncFn();
    } catch (error) {
      addError(error, context);
      throw error;
    }
  }, [addError]);

  return {
    errors,
    addError,
    removeError,
    clearErrors,
    handleAsyncError,
  };
};

/**
 * Custom hook for handling audio-specific errors
 * @returns {Object} Audio error handling utilities
 */
export const useAudioErrorHandler = () => {
  const { addError, ...rest } = useErrorHandler();

  const handleAudioError = useCallback((error, audioUrl = '') => {
    let context = 'Audio Player';
    if (audioUrl) {
      context += ` (${audioUrl})`;
    }

    // Map specific audio errors to user-friendly messages
    let userMessage = '';
    if (error?.name === 'NotAllowedError') {
      userMessage = 'Audio playback was blocked. Please interact with the page first.';
    } else if (error?.name === 'NotSupportedError') {
      userMessage = 'This audio format is not supported by your browser.';
    } else if (error?.name === 'NetworkError') {
      userMessage = 'Failed to load audio. Please check your internet connection.';
    } else {
      userMessage = 'Audio playback failed. Please try again.';
    }

    return addError(userMessage, context);
  }, [addError]);

  return {
    handleAudioError,
    addError,
    ...rest,
  };
};
