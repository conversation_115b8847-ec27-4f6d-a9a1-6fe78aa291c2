import React from 'react';
import { IoInformationCircleOutline } from 'react-icons/io5';

import '../../styles/Card.css';

const Card = ({
  title = "Structural Design - 1",
  price = "₹XXXX",
  duration = "4 months",
  image,
  alt,
  onInfoClick,
  onCardClick,
  className = "",
  showInfoIcon = true
}) => {

  return (
    <div
      className={`card ${onCardClick ? 'card-clickable' : ''} ${className}`}
      onClick={onCardClick}
    >
      <div className="card-content">
        {/* Image Section - Always render container, show placeholder if no image */}
        <div className={`card-image-container ${!image ? 'card-image-placeholder' : ''}`}>
          {image ? (
            <img
              src={image}
              alt={alt || title}
              className="card-image"
            />
          ) : null}
        </div>

        {/* Title */}
        <h3 className="h6 card-title">
          {title}
        </h3>

        {/* Price and Duration Row */}
        <div className="card-footer">
          {/* Price */}
          <span className="body4 card-price">
            Price {price}
          </span>

          {/* Duration and Info Icon */}
          <div className="card-duration-section">
            <span className="body3 card-duration">
              {duration}
            </span>
            {showInfoIcon && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onInfoClick?.();
                }}
                className="card-info-button"
              >
                <IoInformationCircleOutline size={16} />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Card;