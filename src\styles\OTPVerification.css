/* Remove padding from animation-box-inner for OTP verification only */
.otp-verification-box .animation-box-inner {
  padding: 0;
}

/* OTP Verification Box - keeping original background */
.otp-verification-box {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--bg-color);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.otp-title {
 
  color: var(--text-color);
  margin-bottom: 1.5rem;
  text-align: left;
}

/* .otp-description {
  text-align: left;
  margin: 1rem ;
} */

.otp-instruction {
  margin: 0;
  color: var(--text-color);
 
  text-align: left;
}

.otp-container {
  display: flex;
  justify-content: center;
  margin: 1rem 0 0 0;
  gap: 1rem;
}

.otp-input {
  width: 3rem !important;
  height: 3rem !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 10px !important;
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
  font-size: var(--body3-size);
  line-height: var(--body3-line-height);
  font-weight: var(--font-weight-regular);
  text-align: center !important;
  outline: none !important;
  transition: all 0.2s ease !important;
}

.otp-input:focus {
  border-color: var(--secondary-text-color) !important;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2) !important;
}

.otp-input::-webkit-outer-spin-button,
.otp-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.otp-input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.otp-separator {
  width: 0.5rem;
}

.error-message {
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  text-align: center;
}

/* Timer and Resend Section - positioned directly below OTP inputs */
.otp-timer-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.2rem 0 1.5rem 0;
 
}

.otp-timer {
  color: var(--secondary-text-color);
  font-size: var(--body4-size);
  line-height: var(--body4-line-height);
  font-weight: var(--font-weight-regular);
 
}

.resend-link {
  background: transparent;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  
}

.resend-link:hover:not(:disabled) {
  opacity: 0.8;
}

.resend-link.disabled,
.resend-link:disabled {
  color: var(--secondary-text-color);
  cursor: not-allowed;
  text-decoration: none;
  opacity: 0.6;
}

.resend-link.disabled:hover,
.resend-link:disabled:hover {
  opacity: 0.6;
  text-decoration: none;
}

/* Next Button */
.otp-next-button {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background-color: #333;
  color: white;
  border: none;
  border-radius: 10px;
 
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1rem;
}

.otp-next-button:hover:not(:disabled) {
  background-color: #222;
  transform: translateY(-1px);
}

.otp-next-button:disabled {
  /* opacity: 0.5; */
  cursor: not-allowed;
  transform: none;
}

/* Go Back Link */
.otp-go-back {
  background: transparent;
  border: none;
  color: var(--secondary-text-color);
  cursor: pointer;

  padding: 0;
  text-decoration: none;
}

.otp-go-back:hover {
  color: var(--text-color);
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .otp-verification-box {
    padding: 1.5rem;
    margin: 1rem;
  }

  .otp-input {
    width: 2.5rem !important;
    height: 2.5rem !important;
    font-size: 1rem !important;
  }

  .otp-container {
    gap: 0.25rem;
  }

  .otp-title {
    font-size: 1.25rem;
  }

  .otp-timer-section {
    font-size: 0.8rem;
  }
}