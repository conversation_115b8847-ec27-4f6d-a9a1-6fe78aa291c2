import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import SideNavBar from '../SideNavBar';

describe('SideNavBar Component', () => {
  const mockProps = {
    activeTab: 'home',
    onTabChange: vi.fn(),
    onLogout: vi.fn(),
    currentUser: { id: 'test-user', email: '<EMAIL>' }
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render navigation items correctly', () => {
    render(<SideNavBar {...mockProps} />);

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Logout')).toBeInTheDocument();
  });

  it('should render logo section', () => {
    render(<SideNavBar {...mockProps} />);

    expect(screen.getByText('SA')).toBeInTheDocument();
  });

  it('should highlight active tab', () => {
    render(<SideNavBar {...mockProps} activeTab="profile" />);

    const profileButton = screen.getByText('Profile').closest('button');
    expect(profileButton).toHaveClass('active');
  });

  it('should call onTabChange when navigation item is clicked', () => {
    render(<SideNavBar {...mockProps} />);

    const profileButton = screen.getByText('Profile');
    fireEvent.click(profileButton);

    expect(mockProps.onTabChange).toHaveBeenCalledWith('profile');
  });

  it('should call onLogout when logout button is clicked', () => {
    render(<SideNavBar {...mockProps} />);

    const logoutButton = screen.getByText('Logout');
    fireEvent.click(logoutButton);

    expect(mockProps.onLogout).toHaveBeenCalled();
  });

  it('should render with proper CSS classes', () => {
    render(<SideNavBar {...mockProps} />);

    const navbar = screen.getByText('Home').closest('.side-navbar');
    expect(navbar).toBeInTheDocument();
  });
});
