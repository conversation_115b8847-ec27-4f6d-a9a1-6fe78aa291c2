/* Profile Page Styles */
.profile-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-color);
}

/* Main Content */
.profile-main-content {
  flex: 1;
  margin-left: 240px;
  padding: 32px;
  overflow-y: auto;
}

.profile-content-container {
  max-width: 800px;
  margin: 0 auto;
}

/* Text Color Utilities */
.text-primary {
  color: var(--text-color);
}

.text-secondary {
  color: var(--secondary-text-color);
}

/* Profile Section */
.profile-section {
  background-color: var(--bg-color);
  border-radius: 12px;
  padding: 40px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.profile-section h1 {
  margin-bottom: 32px;
  color: var(--text-color);
}

/* Profile Info */
.profile-info {
  margin-bottom: 32px;
}

.profile-field {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;
}

.profile-field:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.profile-label {
  min-width: 120px;
  color: var(--text-color);
  margin-right: 16px;
}

.profile-value {
  flex: 1;
  color: var(--text-color);
}

/* Profile Actions */
.profile-actions {
  display: flex;
  gap: 16px;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
}

.profile-edit-btn,
.profile-settings-btn {
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: var(--font-family-default);
}

.profile-edit-btn {
  background-color: #6366f1;
  color: white;
}

.profile-edit-btn:hover {
  background-color: #4f46e5;
  transform: translateY(-1px);
}

.profile-settings-btn {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.profile-settings-btn:hover {
  background-color: #f3f4f6;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-main-content {
    margin-left: 200px;
    padding: 24px 16px;
  }
  
  .profile-section {
    padding: 24px;
  }
  
  .profile-field {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .profile-label {
    min-width: auto;
    margin-right: 0;
  }
  
  .profile-actions {
    flex-direction: column;
  }
  
  .profile-edit-btn,
  .profile-settings-btn {
    width: 100%;
  }
}

@media (max-width: 640px) {
  .profile-main-content {
    margin-left: 0;
    padding: 16px;
  }
  
  .profile-section {
    padding: 20px;
  }
  
  .profile-section h1 {
    font-size: var(--h3-size);
    margin-bottom: 24px;
  }
}
