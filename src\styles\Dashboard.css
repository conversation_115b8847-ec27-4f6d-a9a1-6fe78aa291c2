/* Dashboard Container - Main Layout */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-color);
}

/* Text Color Utilities */
.text-primary {
  color: var(--text-color);
}

.text-secondary {
  color: var(--secondary-text-color);
}

/* Sidebar Styles */
.dashboard-sidebar {
  width: 240px;
  background-color: var(--bg-color);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  padding: 24px 0;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 100;
}
.dashboard-logo{
  display: flex;
  justify-content: center;
}

.dashboard-logo img{

  /* padding: 0 24px 32px; */
 align-items: center;
  margin-bottom: 24px;
  /* width: 6rem; */
}

/* .logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
  font-size: 18px;
} */

/* Navigation Styles */
.dashboard-nav {
  flex: 1;
  padding: 0 16px;
}

.nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 4px;
  text-align: left;
}

.nav-item:hover {
  background-color: #d6d7dbeb;
}

.nav-item.active {
  background-color:var(--border-color);
  color: var(--text-color);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-label {
  font-size: var(--body3-size);
  font-weight: var(--font-weight-regular);
}

/* Logout Button */
.dashboard-logout {
  padding: 0 16px;
  border-top: 1px solid var(--border-color);
  padding-top: 24px;
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--error-color, #ef4444);
}

.logout-btn:hover {
  background-color: #fef2f2;
}

.logout-icon {
  width: 20px;
  height: 20px;
}

.logout-label {
  font-size: var(--body3-size);
  font-weight: var(--font-weight-regular);
}

/* Main Content */
.dashboard-main {
  flex: 1;
  margin-left: 240px;
  padding: 32px;
  overflow-y: auto;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
}



/* Courses Grid - Exactly 3 cards per row */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  padding: 0;
  margin: 0;
}

.course-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Profile Section */
.profile-section {
  background-color: var(--bg-color);
  border-radius: 12px;
  padding: 32px;
  border: 1px solid var(--border-color);
}

.profile-info {
  margin-top: 24px;
}

.profile-info p {
  margin-bottom: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.profile-info p:last-child {
  border-bottom: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-sidebar {
    width: 200px;
  }

  .dashboard-main {
    margin-left: 200px;
    padding: 24px 16px;
  }

  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (max-width: 640px) {
  .dashboard-sidebar {
    width: 100%;
    position: fixed;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .dashboard-sidebar.open {
    transform: translateX(0);
  }

  .dashboard-main {
    margin-left: 0;
    padding: 16px;
  }

  .courses-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
