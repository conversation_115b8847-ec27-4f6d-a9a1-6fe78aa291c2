import React from 'react';
import Card from '../ui/Card';
import '../../styles/DashboardMainContent.css';

const DashboardMainContent = ({
  activeTab,
  courses,
  currentUser,
  onCourseClick,
  onCourseInfo
}) => {
  const renderHomeContent = () => (
    <div className="courses-grid">
      {courses.map((course) => (
        <Card
          key={course.id}
          title={course.title}
          price={course.price}
          duration={course.duration}
          image={course.image}
          alt={`${course.title} course thumbnail showing construction and engineering concepts`}
          onCardClick={() => onCourseClick(course.id)}
          onInfoClick={() => onCourseInfo(course.id)}
          className="course-card"
        />
      ))}
    </div>
  );

  const renderProfileContent = () => (
    <div className="profile-section">
      <h2 className="h2 text-primary">Profile</h2>
      <div className="profile-info">
        <p className="body2 text-primary">
          <span className="body2-bold">Email:</span> {currentUser?.email}
        </p>
        <p className="body2 text-primary">
          <span className="body2-bold">User ID:</span> {currentUser?.id}
        </p>
        {/* Add more profile information as needed */}
      </div>
    </div>
  );

  const renderCategoriesContent = () => (
    <div className="categories-section">
      <h2 className="h2 text-primary">Categories</h2>
      <p className="body2 text-secondary">Categories content coming soon...</p>
    </div>
  );

  const renderCoursesContent = () => (
    <div className="my-courses-section">
      <h2 className="h2 text-primary">My Courses</h2>
      <p className="body2 text-secondary">Your enrolled courses will appear here...</p>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return renderHomeContent();
      case 'profile':
        return renderProfileContent();
      case 'categories':
        return renderCategoriesContent();
      case 'courses':
        return renderCoursesContent();
      default:
        return renderHomeContent();
    }
  };

  return (
    <div className="dashboard-main-content">
      <div className="content-container">
        {renderContent()}
      </div>
    </div>
  );
};

export default DashboardMainContent;
