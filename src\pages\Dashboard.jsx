import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import Card from '../components/ui/Card';
import { HiHome, HiUser, HiLogout } from 'react-icons/hi';
import '../styles/Dashboard.css';

const Dashboard = () => {
  const navigate = useNavigate();
  const { currentUser, logout, isAuthenticated } = useAuthStore();
  const [activeTab, setActiveTab] = useState('home');

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Sample course data matching the image
  const courses = [
    {
      id: 1,
      title: "Structural Design - 1",
      price: "₹XXXX",
      duration: "4 months",
      image: null, // Will use CSS placeholder
    },
    {
      id: 2,
      title: "Structural Design - 2",
      price: "₹XXXX",
      duration: "4 months",
      image: null,
    },
    {
      id: 3,
      title: "Structural Design - 3",
      price: "₹XXXX",
      duration: "4 months",
      image: null,
    },
    {
      id: 4,
      title: "Strength of Materials",
      price: "₹XXXX",
      duration: "4 months",
      image: null,
    },
    {
      id: 5,
      title: "Structural Analysis",
      price: "₹XXXX",
      duration: "4 months",
      image: null,
    },
    {
      id: 6,
      title: "Engineering Mechanics",
      price: "₹XXXX",
      duration: "4 months",
      image: null,
    }
  ];

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleCourseClick = (courseId) => {
    // Navigate to course details or reading screen
    navigate(`/reading-screen?course=${courseId}`);
  };

  const handleCourseInfo = (courseId) => {
    // Show course info modal or navigate to course info page
    console.log('Show info for course:', courseId);
  };

  const sidebarItems = [
    {
      id: 'home',
      label: 'Home',
      icon: HiHome,
      active: activeTab === 'home'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: HiUser,
      active: activeTab === 'profile'
    }
  ];

  return (
    <div className="dashboard-container">
      {/* Sidebar */}
      <div className="dashboard-sidebar">
        {/* Logo */}
        <div className="dashboard-logo">
          <div className="logo-icon">S</div>
        </div>

        {/* Navigation Items */}
        <nav className="dashboard-nav">
          {sidebarItems.map((item) => {
            const IconComponent = item.icon;
            return (
              <button
                key={item.id}
                className={`nav-item ${item.active ? 'active' : ''}`}
                onClick={() => setActiveTab(item.id)}
              >
                <IconComponent className="nav-icon" />
                <span className="nav-label">{item.label}</span>
              </button>
            );
          })}
        </nav>

        {/* Logout Button */}
        <div className="dashboard-logout">
          <button className="logout-btn" onClick={handleLogout}>
            <HiLogout className="logout-icon" />
            <span className="logout-label">Logout</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="dashboard-main">
        <div className="dashboard-content">
          {activeTab === 'home' && (
            <>
              {/* Welcome Section */}
              <div className="dashboard-header">
                <h1 className="h2">Welcome back, {currentUser?.email || 'Student'}!</h1>
                <p className="body2 text-secondary">Continue your learning journey</p>
              </div>

              {/* Courses Grid */}
              <div className="courses-grid">
                {courses.map((course) => (
                  <Card
                    key={course.id}
                    title={course.title}
                    price={course.price}
                    duration={course.duration}
                    image={course.image}
                    alt={`${course.title} course thumbnail showing construction and engineering concepts`}
                    onCardClick={() => handleCourseClick(course.id)}
                    onInfoClick={() => handleCourseInfo(course.id)}
                    className="course-card"
                  />
                ))}
              </div>
            </>
          )}

          {activeTab === 'profile' && (
            <div className="profile-section">
              <h2 className="h2">Profile</h2>
              <div className="profile-info">
                <p className="body2"><strong>Email:</strong> {currentUser?.email}</p>
                <p className="body2"><strong>User ID:</strong> {currentUser?.id}</p>
                {/* Add more profile information as needed */}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
